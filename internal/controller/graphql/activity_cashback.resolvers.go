package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.78

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/resolvers"
)

// CompleteTask is the resolver for the completeTask field.
func (r *mutationResolver) CompleteTask(ctx context.Context, input gql_model.CompleteTaskInput) (*gql_model.TaskCompletionResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.CompleteTask(ctx, input)
}

// ClaimTaskReward is the resolver for the claimTaskReward field.
func (r *mutationResolver) ClaimTaskReward(ctx context.Context, input gql_model.ClaimTaskRewardInput) (*gql_model.TaskClaimResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.ClaimTaskReward(ctx, input)
}

// ClaimCashback is the resolver for the claimCashback field.
func (r *mutationResolver) ClaimCashback(ctx context.Context, input gql_model.ClaimCashbackInput) (*gql_model.CashbackClaimResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.ClaimCashback(ctx, input)
}

// RefreshTaskList is the resolver for the refreshTaskList field.
func (r *mutationResolver) RefreshTaskList(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.RefreshTaskList(ctx)
}

// ActivityCashbackDashboard is the resolver for the activityCashbackDashboard field.
func (r *queryResolver) ActivityCashbackDashboard(ctx context.Context) (*gql_model.UserDashboardResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.ActivityCashbackDashboard(ctx)
}

// TaskCenter is the resolver for the taskCenter field.
func (r *queryResolver) TaskCenter(ctx context.Context) (*gql_model.TaskCenterResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.TaskCenter(ctx)
}

// TierBenefits is the resolver for the tierBenefits field.
func (r *queryResolver) TierBenefits(ctx context.Context) (*gql_model.TierBenefitsResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.TierBenefits(ctx)
}

// UserTaskProgress is the resolver for the userTaskProgress field.
func (r *queryResolver) UserTaskProgress(ctx context.Context) (*gql_model.UserTaskProgressResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.UserTaskProgress(ctx)
}

// TaskCompletionHistory is the resolver for the taskCompletionHistory field.
func (r *queryResolver) TaskCompletionHistory(ctx context.Context, input *gql_model.TaskCompletionHistoryInput) (*gql_model.TaskCompletionHistoryResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.TaskCompletionHistory(ctx, input)
}

// UserTierInfo is the resolver for the userTierInfo field.
func (r *queryResolver) UserTierInfo(ctx context.Context) (*gql_model.UserTierInfo, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.UserTierInfo(ctx)
}

// TaskCategories is the resolver for the taskCategories field.
func (r *queryResolver) TaskCategories(ctx context.Context) ([]*gql_model.TaskCategory, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.TaskCategories(ctx)
}

// TasksByCategory is the resolver for the tasksByCategory field.
func (r *queryResolver) TasksByCategory(ctx context.Context, categoryName string) ([]*gql_model.ActivityTask, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.TasksByCategory(ctx, categoryName)
}

// !!! WARNING !!!
// The code below was going to be deleted when updating resolvers. It has been copied here so you have
// one last chance to move it out of harms way if you want. There are two reasons this happens:
//  - When renaming or deleting a resolver the old code will be put in here. You can safely delete
//    it when you're done.
//  - You have helper methods in this file. Move them out to keep these resolver files clean.
/*
	func (r *mutationResolver) CreateTask(ctx context.Context, input gql_model.CreateTaskInput) (*gql_model.ActivityTask, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.CreateTask(ctx, input)
}
func (r *mutationResolver) UpdateTask(ctx context.Context, input gql_model.UpdateTaskInput) (*gql_model.ActivityTask, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.UpdateTask(ctx, input)
}
func (r *mutationResolver) DeleteTask(ctx context.Context, taskID string) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.DeleteTask(ctx, taskID)
}
func (r *mutationResolver) CreateTaskCategory(ctx context.Context, input gql_model.CreateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.CreateTaskCategory(ctx, input)
}
func (r *mutationResolver) UpdateTaskCategory(ctx context.Context, input gql_model.UpdateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.UpdateTaskCategory(ctx, input)
}
func (r *mutationResolver) DeleteTaskCategory(ctx context.Context, categoryID string) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.DeleteTaskCategory(ctx, categoryID)
}
func (r *mutationResolver) CreateTierBenefit(ctx context.Context, input gql_model.CreateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.CreateTierBenefit(ctx, input)
}
func (r *mutationResolver) UpdateTierBenefit(ctx context.Context, input gql_model.UpdateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.UpdateTierBenefit(ctx, input)
}
func (r *mutationResolver) DeleteTierBenefit(ctx context.Context, tierBenefitID string) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.DeleteTierBenefit(ctx, tierBenefitID)
}
func (r *mutationResolver) AdminResetDailyTasks(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminResetDailyTasks(ctx)
}
func (r *mutationResolver) AdminResetWeeklyTasks(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminResetWeeklyTasks(ctx)
}
func (r *mutationResolver) AdminResetMonthlyTasks(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminResetMonthlyTasks(ctx)
}
func (r *mutationResolver) AdminRecalculateAllUserTiers(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminRecalculateAllUserTiers(ctx)
}
func (r *mutationResolver) AdminSeedInitialTasks(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminSeedInitialTasks(ctx)
}
func (r *queryResolver) AdminGetAllTasks(ctx context.Context) ([]*gql_model.ActivityTask, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminGetAllTasks(ctx)
}
func (r *queryResolver) AdminGetTaskCompletionStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminTaskCompletionStatsResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminGetTaskCompletionStats(ctx, input)
}
func (r *queryResolver) AdminGetUserActivityStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminUserActivityStatsResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminGetUserActivityStats(ctx, input)
}
func (r *queryResolver) AdminGetTierDistribution(ctx context.Context) (*gql_model.AdminTierDistributionResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminGetTierDistribution(ctx)
}
func (r *queryResolver) AdminGetTopUsers(ctx context.Context, limit *int) ([]*gql_model.UserTierInfo, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminGetTopUsers(ctx, limit)
}
*/
