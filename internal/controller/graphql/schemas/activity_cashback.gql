# Activity Cashback Schema

# Enums
enum TaskType {
  DAILY
  ONE_TIME
  UNLIMITED
  PROGRESSIVE
  MANUAL_UPDATE
}

enum TaskFrequency {
  DAILY
  ONE_TIME
  UNLIMITED
  PROGRESSIVE
  MANUAL
}

enum TaskIdentifier {
  # Daily Tasks
  DAILY_CHECKIN
  MEME_TRADE_DAILY
  PERPETUAL_TRADE_DAILY
  MARKET_PAGE_VIEW
  CONSECUTIVE_CHECKIN
  CONSECUTIVE_TRADING_DAYS

  # Community Tasks
  TWITTER_FOLLOW
  TWITTER_RETWEET
  TWITTER_LIKE
  TELEGRAM_JOIN
  INVITE_FRIENDS
  SHARE_REFERRAL

  # Trading Tasks
  TRADING_POINTS
  ACCUMULATED_TRADING_10K
  ACCUMULATED_TRADING_50K
  ACCUMULATED_TRADING_100K
  ACCUMULATED_TRADING_500K
}

enum TaskStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  CLAIMED
  EXPIRED
}

enum ClaimType {
  TRADING_CASHBACK
  TASK_REWARD
  TIER_BONUS
  REFERRAL_BONUS
}

enum ClaimStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

# Types
type TaskCategory {
  id: ID!
  name: String!
  displayName: String!
  description: String
  icon: String
  sortOrder: Int!
  isActive: Boolean!
  createdAt: Time!
  updatedAt: Time!
  tasks: [ActivityTask!]
}

type ActivityTask {
  id: ID!
  categoryId: ID!
  name: String!
  description: String
  taskType: TaskType!
  frequency: TaskFrequency!
  taskIdentifier: TaskIdentifier # New field for unique task identification
  points: Int!
  maxCompletions: Int
  resetPeriod: String
  conditions: String # JSON string
  actionTarget: String
  verificationMethod: String
  externalLink: String
  isActive: Boolean!
  startDate: Time
  endDate: Time
  sortOrder: Int!
  createdAt: Time!
  updatedAt: Time!
  category: TaskCategory
  userProgress: UserTaskProgress
}

type UserTaskProgress {
  id: ID!
  userId: ID!
  taskId: ID!
  status: TaskStatus!
  progressValue: Int!
  targetValue: Int
  completionCount: Int!
  pointsEarned: Int!
  lastCompletedAt: Time
  lastResetAt: Time
  streakCount: Int!
  metadata: String # JSON string
  createdAt: Time!
  updatedAt: Time!
  task: ActivityTask
  progressPercentage: Float!
  canBeClaimed: Boolean!
}

type UserTierInfo {
  userId: ID!
  currentTier: Int!
  totalPoints: Int!
  pointsThisMonth: Int!
  tradingVolumeUsd: Float!
  activeDaysThisMonth: Int!
  cumulativeCashbackUsd: Float!
  claimableCashbackUsd: Float!
  claimedCashbackUsd: Float!
  lastActivityDate: Time
  tierUpgradedAt: Time
  monthlyResetAt: Time
  createdAt: Time!
  updatedAt: Time!
  tierBenefit: TierBenefit
  userRank: Int
}

type TierBenefit {
  id: ID!
  tierLevel: Int!
  tierName: String!
  minPoints: Int!
  cashbackPercentage: Float!
  benefitsDescription: String
  tierColor: String
  tierIcon: String
  isActive: Boolean!
  createdAt: Time!
  updatedAt: Time!
}

type ActivityCashbackClaim {
  id: ID!
  userId: ID!
  claimType: ClaimType!
  totalAmountUsd: Float!
  totalAmountSol: Float!
  transactionHash: String
  status: ClaimStatus!
  claimedAt: Time!
  processedAt: Time
  metadata: String # JSON string
  createdAt: Time!
  updatedAt: Time!
}

type TaskCompletionHistory {
  id: ID!
  userId: ID!
  taskId: ID!
  pointsAwarded: Int!
  completionDate: Time!
  verificationData: String # JSON string
  createdAt: Time!
  task: ActivityTask
}

type UserDashboard {
  userTierInfo: UserTierInfo!
  tierBenefit: TierBenefit!
  nextTier: TierBenefit
  pointsToNextTier: Int!
  claimableCashback: Float!
  recentClaims: [ActivityCashbackClaim!]!
  userRank: Int!
}

type TaskCenter {
  categories: [TaskCategoryWithTasks!]!
  userProgress: [UserTaskProgress!]!
  completedToday: Int!
  pointsEarnedToday: Int!
  streakTasks: [UserTaskProgress!]!
}

type TaskCategoryWithTasks {
  category: TaskCategory!
  tasks: [TaskWithProgress!]!
}

type TaskWithProgress {
  task: ActivityTask!
  progress: UserTaskProgress
}

# Response Types
type UserDashboardResponse {
  success: Boolean!
  message: String!
  data: UserDashboard
}

type TaskCenterResponse {
  success: Boolean!
  message: String!
  data: TaskCenter
}

type TaskCompletionResponse {
  success: Boolean!
  message: String!
  pointsAwarded: Int!
  newTierLevel: Int
  tierUpgraded: Boolean!
}

type TaskClaimResponse {
  success: Boolean!
  message: String!
  pointsClaimed: Int!
}

type CashbackClaimResponse {
  success: Boolean!
  message: String!
  claimId: ID!
  amountUsd: Float!
  amountSol: Float!
}

type TierBenefitsResponse {
  success: Boolean!
  message: String!
  data: [TierBenefit!]!
}

type TierBenefitResponse {
  success: Boolean!
  message: String!
  data: TierBenefit
}

type UserTaskProgressResponse {
  success: Boolean!
  message: String!
  data: [UserTaskProgress!]!
}

type TaskCompletionHistoryResponse {
  success: Boolean!
  message: String!
  data: [TaskCompletionHistory!]!
  total: Int!
}

# Input Types
input CompleteTaskInput {
  taskId: ID!
  verificationData: String # JSON string
}

input ClaimTaskRewardInput {
  taskId: ID!
}

input ClaimCashbackInput {
  amountUsd: Float!
}

input CreateTaskInput {
  categoryId: ID!
  name: String!
  description: String
  taskType: TaskType!
  frequency: TaskFrequency!
  taskIdentifier: TaskIdentifier # Optional unique identifier for task processing
  points: Int!
  maxCompletions: Int
  resetPeriod: String
  conditions: String # JSON string
  actionTarget: String
  verificationMethod: String
  externalLink: String
  startDate: Time
  endDate: Time
  sortOrder: Int
}

input UpdateTaskInput {
  id: ID!
  categoryId: ID
  name: String
  description: String
  taskType: TaskType
  frequency: TaskFrequency
  points: Int
  maxCompletions: Int
  resetPeriod: String
  conditions: String # JSON string
  actionTarget: String
  verificationMethod: String
  externalLink: String
  isActive: Boolean
  startDate: Time
  endDate: Time
  sortOrder: Int
}

input CreateTaskCategoryInput {
  name: String!
  displayName: String!
  description: String
  icon: String
  sortOrder: Int
}

input UpdateTaskCategoryInput {
  id: ID!
  name: String
  displayName: String
  description: String
  icon: String
  sortOrder: Int
  isActive: Boolean
}

input CreateTierBenefitInput {
  tierLevel: Int!
  tierName: String!
  minPoints: Int!
  cashbackPercentage: Float!
  benefitsDescription: String
  tierColor: String
  tierIcon: String
}

input UpdateTierBenefitInput {
  id: ID!
  tierLevel: Int
  tierName: String
  minPoints: Int
  cashbackPercentage: Float
  benefitsDescription: String
  tierColor: String
  tierIcon: String
  isActive: Boolean
}

input TaskCompletionHistoryInput {
  taskId: ID
  startDate: Time
  endDate: Time
  limit: Int
  offset: Int
}

# Admin input types
input AdminStatsInput {
  startDate: Time!
  endDate: Time!
}

# Admin response types
type AdminTaskCompletionStatsResponse {
  success: Boolean!
  message: String!
  data: AdminTaskCompletionStats
}

type AdminTaskCompletionStats {
  taskCompletions: [TaskCompletionStat!]!
  startDate: Time!
  endDate: Time!
  totalTasks: Int!
}

type TaskCompletionStat {
  taskName: String!
  completionCount: Int!
}

type AdminUserActivityStatsResponse {
  success: Boolean!
  message: String!
  data: AdminUserActivityStats
}

type AdminUserActivityStats {
  dailyCompletions: [DailyCompletionStat!]!
  startDate: Time!
  endDate: Time!
}

type DailyCompletionStat {
  date: String!
  completionCount: Int!
}

type AdminTierDistributionResponse {
  success: Boolean!
  message: String!
  data: [TierDistributionStat!]!
}

type TierDistributionStat {
  tierLevel: Int!
  userCount: Int!
}

# Queries
extend type Query {
  # Get user dashboard for activity cashback
  activityCashbackDashboard: UserDashboardResponse! @auth

  # Get task center with all tasks and progress
  taskCenter: TaskCenterResponse! @auth

  # Get all tier benefits
  tierBenefits: TierBenefitsResponse! @auth

  # Get user task progress
  userTaskProgress: UserTaskProgressResponse! @auth

  # Get task completion history
  taskCompletionHistory(input: TaskCompletionHistoryInput): TaskCompletionHistoryResponse! @auth

  # Get user tier info
  userTierInfo: UserTierInfo @auth

  # Get task categories
  taskCategories: [TaskCategory!]! @auth

  # Get tasks by category
  tasksByCategory(categoryName: String!): [ActivityTask!]! @auth

  # Admin: Get all tasks
  adminGetAllTasks: [ActivityTask!]! @adminAuth

  # Admin: Get task completion statistics
  adminGetTaskCompletionStats(input: AdminStatsInput!): AdminTaskCompletionStatsResponse! @adminAuth

  # Admin: Get user activity statistics
  adminGetUserActivityStats(input: AdminStatsInput!): AdminUserActivityStatsResponse! @adminAuth

  # Admin: Get tier distribution
  adminGetTierDistribution: AdminTierDistributionResponse! @adminAuth

  # Admin: Get top users by points
  adminGetTopUsers(limit: Int = 10): [UserTierInfo!]! @adminAuth
}

# Mutations
extend type Mutation {
  # Complete a task
  completeTask(input: CompleteTaskInput!): TaskCompletionResponse! @auth

  # Claim task reward
  claimTaskReward(input: ClaimTaskRewardInput!): TaskClaimResponse! @auth

  # Claim cashback
  claimCashback(input: ClaimCashbackInput!): CashbackClaimResponse! @auth

  # Refresh task list
  refreshTaskList: Boolean! @auth

  # Admin: Create task
  createTask(input: CreateTaskInput!): ActivityTask! @adminAuth

  # Admin: Update task
  updateTask(input: UpdateTaskInput!): ActivityTask! @adminAuth

  # Admin: Delete task
  deleteTask(taskId: ID!): Boolean! @adminAuth

  # Admin: Create task category
  createTaskCategory(input: CreateTaskCategoryInput!): TaskCategory! @adminAuth

  # Admin: Update task category
  updateTaskCategory(input: UpdateTaskCategoryInput!): TaskCategory! @adminAuth

  # Admin: Delete task category
  deleteTaskCategory(categoryId: ID!): Boolean! @adminAuth

  # Admin: Create tier benefit
  createTierBenefit(input: CreateTierBenefitInput!): TierBenefitResponse! @adminAuth

  # Admin: Update tier benefit
  updateTierBenefit(input: UpdateTierBenefitInput!): TierBenefitResponse! @adminAuth

  # Admin: Delete tier benefit
  deleteTierBenefit(tierBenefitId: ID!): Boolean! @adminAuth

  # Admin: Reset daily tasks
  adminResetDailyTasks: Boolean! @adminAuth

  # Admin: Reset weekly tasks
  adminResetWeeklyTasks: Boolean! @adminAuth

  # Admin: Reset monthly tasks
  adminResetMonthlyTasks: Boolean! @adminAuth

  # Admin: Recalculate all user tiers
  adminRecalculateAllUserTiers: Boolean! @adminAuth

  # Admin: Seed initial tasks
  adminSeedInitialTasks: Boolean! @adminAuth
}
