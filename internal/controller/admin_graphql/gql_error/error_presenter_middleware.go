package gql_error

import (
	"context"
	"errors"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/gqlerror"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

// CustomErrorPresenter handles GraphQL errors for admin endpoint
func CustomErrorPresenter(ctx context.Context, e error) *gqlerror.Error {
	err := graphql.DefaultErrorPresenter(ctx, e)

	// Log the error for debugging
	global.GVA_LOG.Error("Admin GraphQL Error", 
		zap.String("message", err.Message),
		zap.String("path", err.Path.String()),
		zap.Any("extensions", err.Extensions),
	)

	// Handle specific error types
	switch {
	case errors.Is(e, utils.ErrAccessTokenInvalid):
		err.Message = "Admin authentication required"
		err.Extensions = map[string]interface{}{
			"code": "ADMIN_AUTH_REQUIRED",
		}
	case errors.Is(e, utils.ErrInvalidInput):
		err.Message = "Invalid input provided"
		err.Extensions = map[string]interface{}{
			"code": "INVALID_INPUT",
		}
	default:
		// For production, don't expose internal errors
		if global.GVA_CONFIG.System.Env == "production" {
			err.Message = "Internal server error"
			err.Extensions = map[string]interface{}{
				"code": "INTERNAL_ERROR",
			}
		}
	}

	return err
}
