package admin_graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.78

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin_graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/resolvers"
)

// CreateTask is the resolver for the createTask field.
func (r *mutationResolver) CreateTask(ctx context.Context, input gql_model.CreateTaskInput) (*gql_model.ActivityTask, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()

	// Convert admin GraphQL input to user GraphQL input
	userInput := convertCreateTaskInputToUser(input)
	userResult, err := activityCashbackResolver.CreateTask(ctx, userInput)
	if err != nil {
		return nil, err
	}

	// Convert user GraphQL result to admin GraphQL result
	return convertActivityTaskToAdmin(userResult), nil
}

// UpdateTask is the resolver for the updateTask field.
func (r *mutationResolver) UpdateTask(ctx context.Context, input gql_model.UpdateTaskInput) (*gql_model.ActivityTask, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()

	// Convert admin GraphQL input to user GraphQL input
	userInput := convertUpdateTaskInputToUser(input)
	userResult, err := activityCashbackResolver.UpdateTask(ctx, userInput)
	if err != nil {
		return nil, err
	}

	// Convert user GraphQL result to admin GraphQL result
	return convertActivityTaskToAdmin(userResult), nil
}

// DeleteTask is the resolver for the deleteTask field.
func (r *mutationResolver) DeleteTask(ctx context.Context, taskID string) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.DeleteTask(ctx, taskID)
}

// CreateTaskCategory is the resolver for the createTaskCategory field.
func (r *mutationResolver) CreateTaskCategory(ctx context.Context, input gql_model.CreateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()

	// Convert admin GraphQL input to user GraphQL input
	userInput := convertCreateTaskCategoryInputToUser(input)
	userResult, err := activityCashbackResolver.CreateTaskCategory(ctx, userInput)
	if err != nil {
		return nil, err
	}

	// Convert user GraphQL result to admin GraphQL result
	return convertTaskCategoryToAdmin(userResult), nil
}

// UpdateTaskCategory is the resolver for the updateTaskCategory field.
func (r *mutationResolver) UpdateTaskCategory(ctx context.Context, input gql_model.UpdateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()

	// Convert admin GraphQL input to user GraphQL input
	userInput := convertUpdateTaskCategoryInputToUser(input)
	userResult, err := activityCashbackResolver.UpdateTaskCategory(ctx, userInput)
	if err != nil {
		return nil, err
	}

	// Convert user GraphQL result to admin GraphQL result
	return convertTaskCategoryToAdmin(userResult), nil
}

// DeleteTaskCategory is the resolver for the deleteTaskCategory field.
func (r *mutationResolver) DeleteTaskCategory(ctx context.Context, categoryID string) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.DeleteTaskCategory(ctx, categoryID)
}

// CreateTierBenefit is the resolver for the createTierBenefit field.
func (r *mutationResolver) CreateTierBenefit(ctx context.Context, input gql_model.CreateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()

	// Convert admin GraphQL input to user GraphQL input
	userInput := convertCreateTierBenefitInputToUser(input)
	userResult, err := activityCashbackResolver.CreateTierBenefit(ctx, userInput)
	if err != nil {
		return nil, err
	}

	// Convert user GraphQL result to admin GraphQL result
	return convertTierBenefitResponseToAdmin(userResult), nil
}

// UpdateTierBenefit is the resolver for the updateTierBenefit field.
func (r *mutationResolver) UpdateTierBenefit(ctx context.Context, input gql_model.UpdateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()

	// Convert admin GraphQL input to user GraphQL input
	userInput := convertUpdateTierBenefitInputToUser(input)
	userResult, err := activityCashbackResolver.UpdateTierBenefit(ctx, userInput)
	if err != nil {
		return nil, err
	}

	// Convert user GraphQL result to admin GraphQL result
	return convertTierBenefitResponseToAdmin(userResult), nil
}

// DeleteTierBenefit is the resolver for the deleteTierBenefit field.
func (r *mutationResolver) DeleteTierBenefit(ctx context.Context, tierBenefitID string) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.DeleteTierBenefit(ctx, tierBenefitID)
}

// AdminResetDailyTasks is the resolver for the adminResetDailyTasks field.
func (r *mutationResolver) AdminResetDailyTasks(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminResetDailyTasks(ctx)
}

// AdminResetWeeklyTasks is the resolver for the adminResetWeeklyTasks field.
func (r *mutationResolver) AdminResetWeeklyTasks(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminResetWeeklyTasks(ctx)
}

// AdminResetMonthlyTasks is the resolver for the adminResetMonthlyTasks field.
func (r *mutationResolver) AdminResetMonthlyTasks(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminResetMonthlyTasks(ctx)
}

// AdminRecalculateAllUserTiers is the resolver for the adminRecalculateAllUserTiers field.
func (r *mutationResolver) AdminRecalculateAllUserTiers(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminRecalculateAllUserTiers(ctx)
}

// AdminSeedInitialTasks is the resolver for the adminSeedInitialTasks field.
func (r *mutationResolver) AdminSeedInitialTasks(ctx context.Context) (bool, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()
	return activityCashbackResolver.AdminSeedInitialTasks(ctx)
}

// AdminGetAllTasks is the resolver for the adminGetAllTasks field.
func (r *queryResolver) AdminGetAllTasks(ctx context.Context) ([]*gql_model.ActivityTask, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()

	userTasks, err := activityCashbackResolver.AdminGetAllTasks(ctx)
	if err != nil {
		return nil, err
	}

	// Convert user GraphQL results to admin GraphQL results
	var adminTasks []*gql_model.ActivityTask
	for _, userTask := range userTasks {
		adminTasks = append(adminTasks, convertActivityTaskToAdmin(userTask))
	}

	return adminTasks, nil
}

// AdminGetTaskCompletionStats is the resolver for the adminGetTaskCompletionStats field.
func (r *queryResolver) AdminGetTaskCompletionStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminTaskCompletionStatsResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()

	// Convert admin GraphQL input to user GraphQL input
	userInput := convertAdminStatsInputToUser(input)
	userResult, err := activityCashbackResolver.AdminGetTaskCompletionStats(ctx, userInput)
	if err != nil {
		return nil, err
	}

	// Convert user GraphQL result to admin GraphQL result
	return convertAdminTaskCompletionStatsResponseToAdmin(userResult), nil
}

// AdminGetUserActivityStats is the resolver for the adminGetUserActivityStats field.
func (r *queryResolver) AdminGetUserActivityStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminUserActivityStatsResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()

	// Convert admin GraphQL input to user GraphQL input
	userInput := convertAdminStatsInputToUser(input)
	userResult, err := activityCashbackResolver.AdminGetUserActivityStats(ctx, userInput)
	if err != nil {
		return nil, err
	}

	// Convert user GraphQL result to admin GraphQL result
	return convertAdminUserActivityStatsResponseToAdmin(userResult), nil
}

// AdminGetTierDistribution is the resolver for the adminGetTierDistribution field.
func (r *queryResolver) AdminGetTierDistribution(ctx context.Context) (*gql_model.AdminTierDistributionResponse, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()

	userResult, err := activityCashbackResolver.AdminGetTierDistribution(ctx)
	if err != nil {
		return nil, err
	}

	// Convert user GraphQL result to admin GraphQL result
	return convertAdminTierDistributionResponseToAdmin(userResult), nil
}

// AdminGetTopUsers is the resolver for the adminGetTopUsers field.
func (r *queryResolver) AdminGetTopUsers(ctx context.Context, limit *int) ([]*gql_model.UserTierInfo, error) {
	activityCashbackResolver := resolvers.NewActivityCashbackResolver()

	userResults, err := activityCashbackResolver.AdminGetTopUsers(ctx, limit)
	if err != nil {
		return nil, err
	}

	// Convert user GraphQL results to admin GraphQL results
	var adminResults []*gql_model.UserTierInfo
	for _, userResult := range userResults {
		adminResults = append(adminResults, convertUserTierInfoToAdmin(userResult))
	}

	return adminResults, nil
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
