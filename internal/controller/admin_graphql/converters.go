package admin_graphql

import (
	admin_gql "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin_graphql/gql_model"
	user_gql "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
)

// Convert admin GraphQL CreateTaskInput to user GraphQL CreateTaskInput
func convertCreateTaskInputToUser(input admin_gql.CreateTaskInput) user_gql.CreateTaskInput {
	userInput := user_gql.CreateTaskInput{
		Name:       input.Name,
		CategoryID: input.CategoryID,
		TaskType:   user_gql.TaskType(input.Type),
		Frequency:  user_gql.TaskFrequency(input.Frequency),
		Points:     input.PointsReward,
	}

	if input.Description != nil {
		userInput.Description = input.Description
	}
	if input.MaxCompletions != nil {
		userInput.MaxCompletions = input.MaxCompletions
	}
	if input.StartDate != nil {
		userInput.StartDate = input.StartDate
	}
	if input.EndDate != nil {
		userInput.EndDate = input.EndDate
	}
	// Note: User GraphQL doesn't have Requirements field, so we skip it

	return userInput
}

// Convert admin GraphQL UpdateTaskInput to user GraphQL UpdateTaskInput
func convertUpdateTaskInputToUser(input admin_gql.UpdateTaskInput) user_gql.UpdateTaskInput {
	userInput := user_gql.UpdateTaskInput{
		ID: input.TaskID,
	}

	if input.Name != nil {
		userInput.Name = input.Name
	}
	if input.Description != nil {
		userInput.Description = input.Description
	}
	if input.CategoryID != nil {
		userInput.CategoryID = input.CategoryID
	}
	if input.Type != nil {
		taskType := user_gql.TaskType(*input.Type)
		userInput.TaskType = &taskType
	}
	if input.Frequency != nil {
		frequency := user_gql.TaskFrequency(*input.Frequency)
		userInput.Frequency = &frequency
	}
	if input.PointsReward != nil {
		userInput.Points = input.PointsReward
	}
	if input.MaxCompletions != nil {
		userInput.MaxCompletions = input.MaxCompletions
	}
	if input.IsActive != nil {
		userInput.IsActive = input.IsActive
	}
	if input.StartDate != nil {
		userInput.StartDate = input.StartDate
	}
	if input.EndDate != nil {
		userInput.EndDate = input.EndDate
	}
	// Note: User GraphQL doesn't have Requirements field, so we skip it

	return userInput
}

// Note: User GraphQL doesn't have TaskRequirementsInput, so we skip this converter

// Convert user GraphQL ActivityTask to admin GraphQL ActivityTask
func convertActivityTaskToAdmin(userTask *user_gql.ActivityTask) *admin_gql.ActivityTask {
	if userTask == nil {
		return nil
	}

	adminTask := &admin_gql.ActivityTask{
		ID:             userTask.ID,
		Name:           userTask.Name,
		Description:    userTask.Description,
		Category:       convertTaskCategoryToAdmin(userTask.Category),
		Type:           admin_gql.TaskType(userTask.TaskType),
		Frequency:      admin_gql.TaskFrequency(userTask.Frequency),
		PointsReward:   userTask.Points,
		CashbackReward: 0.0, // User model doesn't have CashbackReward, set to 0
		MaxCompletions: userTask.MaxCompletions,
		IsActive:       userTask.IsActive,
		StartDate:      userTask.StartDate,
		EndDate:        userTask.EndDate,
		CreatedAt:      userTask.CreatedAt,
		UpdatedAt:      userTask.UpdatedAt,
	}

	// Note: User GraphQL doesn't have Requirements field, so we skip it

	return adminTask
}

// Convert user GraphQL TaskCategory to admin GraphQL TaskCategory
func convertTaskCategoryToAdmin(userCategory *user_gql.TaskCategory) *admin_gql.TaskCategory {
	if userCategory == nil {
		return nil
	}

	return &admin_gql.TaskCategory{
		ID:          userCategory.ID,
		Name:        userCategory.Name,
		DisplayName: userCategory.DisplayName,
		Description: userCategory.Description,
		IsActive:    userCategory.IsActive,
		SortOrder:   userCategory.SortOrder,
		CreatedAt:   userCategory.CreatedAt,
		UpdatedAt:   userCategory.UpdatedAt,
	}
}

// Note: User GraphQL doesn't have TaskRequirements, so we skip this converter

// Convert admin GraphQL CreateTaskCategoryInput to user GraphQL CreateTaskCategoryInput
func convertCreateTaskCategoryInputToUser(input admin_gql.CreateTaskCategoryInput) user_gql.CreateTaskCategoryInput {
	userInput := user_gql.CreateTaskCategoryInput{
		Name:        input.Name,
		DisplayName: input.DisplayName,
		SortOrder:   input.SortOrder,
	}

	if input.Description != nil {
		userInput.Description = input.Description
	}

	return userInput
}

// Convert admin GraphQL UpdateTaskCategoryInput to user GraphQL UpdateTaskCategoryInput
func convertUpdateTaskCategoryInputToUser(input admin_gql.UpdateTaskCategoryInput) user_gql.UpdateTaskCategoryInput {
	userInput := user_gql.UpdateTaskCategoryInput{
		ID: input.CategoryID,
	}

	if input.Name != nil {
		userInput.Name = input.Name
	}
	if input.DisplayName != nil {
		userInput.DisplayName = input.DisplayName
	}
	if input.Description != nil {
		userInput.Description = input.Description
	}
	if input.IsActive != nil {
		userInput.IsActive = input.IsActive
	}
	if input.SortOrder != nil {
		userInput.SortOrder = input.SortOrder
	}

	return userInput
}

// Convert admin GraphQL CreateTierBenefitInput to user GraphQL CreateTierBenefitInput
func convertCreateTierBenefitInputToUser(input admin_gql.CreateTierBenefitInput) user_gql.CreateTierBenefitInput {
	userInput := user_gql.CreateTierBenefitInput{
		TierLevel:          input.TierLevel,
		TierName:           input.BenefitType, // Map BenefitType to TierName
		MinPoints:          0,                 // Default value
		CashbackPercentage: input.BenefitValue,
	}

	if input.Description != nil {
		userInput.BenefitsDescription = input.Description
	}

	return userInput
}

// Convert admin GraphQL UpdateTierBenefitInput to user GraphQL UpdateTierBenefitInput
func convertUpdateTierBenefitInputToUser(input admin_gql.UpdateTierBenefitInput) user_gql.UpdateTierBenefitInput {
	userInput := user_gql.UpdateTierBenefitInput{
		ID: input.TierBenefitID,
	}

	if input.TierLevel != nil {
		userInput.TierLevel = input.TierLevel
	}
	if input.BenefitType != nil {
		userInput.TierName = input.BenefitType
	}
	if input.BenefitValue != nil {
		userInput.CashbackPercentage = input.BenefitValue
	}
	if input.Description != nil {
		userInput.BenefitsDescription = input.Description
	}
	if input.IsActive != nil {
		userInput.IsActive = input.IsActive
	}

	return userInput
}

// Convert user GraphQL TierBenefitResponse to admin GraphQL TierBenefitResponse
func convertTierBenefitResponseToAdmin(userResp *user_gql.TierBenefitResponse) *admin_gql.TierBenefitResponse {
	if userResp == nil {
		return nil
	}

	adminResp := &admin_gql.TierBenefitResponse{
		ID:           userResp.Data.ID,
		TierLevel:    userResp.Data.TierLevel,
		BenefitType:  userResp.Data.TierName,
		BenefitValue: userResp.Data.CashbackPercentage,
		IsActive:     userResp.Data.IsActive,
		CreatedAt:    userResp.Data.CreatedAt,
		UpdatedAt:    userResp.Data.UpdatedAt,
	}

	if userResp.Data.BenefitsDescription != nil {
		adminResp.Description = userResp.Data.BenefitsDescription
	}

	return adminResp
}

// Convert admin GraphQL AdminStatsInput to user GraphQL AdminStatsInput
func convertAdminStatsInputToUser(input admin_gql.AdminStatsInput) user_gql.AdminStatsInput {
	return user_gql.AdminStatsInput{
		StartDate: input.StartDate,
		EndDate:   input.EndDate,
	}
}

// Convert user GraphQL AdminTaskCompletionStatsResponse to admin GraphQL AdminTaskCompletionStatsResponse
func convertAdminTaskCompletionStatsResponseToAdmin(userResp *user_gql.AdminTaskCompletionStatsResponse) *admin_gql.AdminTaskCompletionStatsResponse {
	if userResp == nil {
		return nil
	}

	adminResp := &admin_gql.AdminTaskCompletionStatsResponse{
		Success: userResp.Success,
		Message: userResp.Message,
	}

	if userResp.Data != nil {
		adminResp.Data = convertAdminTaskCompletionStatsToAdmin(userResp.Data)
	}

	return adminResp
}

// Convert user GraphQL AdminTaskCompletionStats to admin GraphQL AdminTaskCompletionStats
func convertAdminTaskCompletionStatsToAdmin(userStats *user_gql.AdminTaskCompletionStats) *admin_gql.AdminTaskCompletionStats {
	if userStats == nil {
		return nil
	}

	adminStats := &admin_gql.AdminTaskCompletionStats{
		StartDate:  userStats.StartDate,
		EndDate:    userStats.EndDate,
		TotalTasks: userStats.TotalTasks,
	}

	for _, userStat := range userStats.TaskCompletions {
		adminStats.TaskCompletions = append(adminStats.TaskCompletions, &admin_gql.TaskCompletionStat{
			TaskName:        userStat.TaskName,
			CompletionCount: userStat.CompletionCount,
		})
	}

	return adminStats
}

// Convert user GraphQL AdminUserActivityStatsResponse to admin GraphQL AdminUserActivityStatsResponse
func convertAdminUserActivityStatsResponseToAdmin(userResp *user_gql.AdminUserActivityStatsResponse) *admin_gql.AdminUserActivityStatsResponse {
	if userResp == nil {
		return nil
	}

	adminResp := &admin_gql.AdminUserActivityStatsResponse{
		Success: userResp.Success,
		Message: userResp.Message,
	}

	if userResp.Data != nil {
		adminResp.Data = convertAdminUserActivityStatsToAdmin(userResp.Data)
	}

	return adminResp
}

// Convert user GraphQL AdminUserActivityStats to admin GraphQL AdminUserActivityStats
func convertAdminUserActivityStatsToAdmin(userStats *user_gql.AdminUserActivityStats) *admin_gql.AdminUserActivityStats {
	if userStats == nil {
		return nil
	}

	adminStats := &admin_gql.AdminUserActivityStats{
		StartDate: userStats.StartDate,
		EndDate:   userStats.EndDate,
	}

	for _, userStat := range userStats.DailyCompletions {
		adminStats.DailyCompletions = append(adminStats.DailyCompletions, &admin_gql.DailyCompletionStat{
			Date:            userStat.Date,
			CompletionCount: userStat.CompletionCount,
		})
	}

	return adminStats
}

// Convert user GraphQL AdminTierDistributionResponse to admin GraphQL AdminTierDistributionResponse
func convertAdminTierDistributionResponseToAdmin(userResp *user_gql.AdminTierDistributionResponse) *admin_gql.AdminTierDistributionResponse {
	if userResp == nil {
		return nil
	}

	adminResp := &admin_gql.AdminTierDistributionResponse{
		Success: userResp.Success,
		Message: userResp.Message,
	}

	for _, userStat := range userResp.Data {
		adminResp.Data = append(adminResp.Data, &admin_gql.TierDistributionStat{
			TierLevel: userStat.TierLevel,
			UserCount: userStat.UserCount,
		})
	}

	return adminResp
}

// Convert user GraphQL UserTierInfo to admin GraphQL UserTierInfo
func convertUserTierInfoToAdmin(userInfo *user_gql.UserTierInfo) *admin_gql.UserTierInfo {
	if userInfo == nil {
		return nil
	}

	return &admin_gql.UserTierInfo{
		UserID:         userInfo.UserID,
		CurrentTier:    userInfo.CurrentTier,
		TotalPoints:    userInfo.TotalPoints,
		TotalCashback:  userInfo.CumulativeCashbackUsd,
		CompletedTasks: 0, // This field doesn't exist in user model, set to 0
		LastActivityAt: userInfo.LastActivityDate,
	}
}
