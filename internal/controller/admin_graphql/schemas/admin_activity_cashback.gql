# Admin Activity Cashback Types and Inputs

# Admin Statistics Input
input AdminStatsInput {
  startDate: Time!
  endDate: Time!
}

# Admin Task Completion Statistics
type AdminTaskCompletionStatsResponse {
  success: Boolean!
  message: String!
  data: AdminTaskCompletionStats
}

type AdminTaskCompletionStats {
  taskCompletions: [TaskCompletionStat!]!
  startDate: Time!
  endDate: Time!
  totalTasks: Int!
}

type TaskCompletionStat {
  taskName: String!
  completionCount: Int!
}

# Admin User Activity Statistics
type AdminUserActivityStatsResponse {
  success: Boolean!
  message: String!
  data: AdminUserActivityStats
}

type AdminUserActivityStats {
  dailyCompletions: [DailyCompletionStat!]!
  startDate: Time!
  endDate: Time!
}

type DailyCompletionStat {
  date: String!
  completionCount: Int!
}

# Admin Tier Distribution
type AdminTierDistributionResponse {
  success: Boolean!
  message: String!
  data: [TierDistributionStat!]!
}

type TierDistributionStat {
  tierLevel: Int!
  userCount: Int!
}

# Activity Task Types
type ActivityTask {
  id: ID!
  name: String!
  description: String
  category: TaskCategory!
  type: TaskType!
  frequency: TaskFrequency!
  pointsReward: Int!
  cashbackReward: Float!
  maxCompletions: Int
  isActive: Boolean!
  startDate: Time
  endDate: Time
  requirements: TaskRequirements
  createdAt: Time!
  updatedAt: Time!
}

type TaskCategory {
  id: ID!
  name: String!
  displayName: String!
  description: String
  isActive: Boolean!
  sortOrder: Int!
  createdAt: Time!
  updatedAt: Time!
}

enum TaskType {
  TRADING
  SOCIAL
  REFERRAL
  LOGIN
  MARKET_CHECK
  CUSTOM
}

enum TaskFrequency {
  DAILY
  WEEKLY
  MONTHLY
  ONE_TIME
}

type TaskRequirements {
  minTradeVolume: Float
  minTradeCount: Int
  socialAction: String
  referralCount: Int
  loginDays: Int
  customData: String
}

# User Tier Info
type UserTierInfo {
  userId: String!
  currentTier: Int!
  totalPoints: Int!
  totalCashback: Float!
  completedTasks: Int!
  lastActivityAt: Time
}

# Task Management Inputs
input CreateTaskInput {
  name: String!
  description: String
  categoryId: ID!
  type: TaskType!
  frequency: TaskFrequency!
  pointsReward: Int!
  cashbackReward: Float!
  maxCompletions: Int
  isActive: Boolean = true
  startDate: Time
  endDate: Time
  requirements: TaskRequirementsInput
}

input UpdateTaskInput {
  taskId: ID!
  name: String
  description: String
  categoryId: ID
  type: TaskType
  frequency: TaskFrequency
  pointsReward: Int
  cashbackReward: Float
  maxCompletions: Int
  isActive: Boolean
  startDate: Time
  endDate: Time
  requirements: TaskRequirementsInput
}

input TaskRequirementsInput {
  minTradeVolume: Float
  minTradeCount: Int
  socialAction: String
  referralCount: Int
  loginDays: Int
  customData: String
}

# Task Category Management Inputs
input CreateTaskCategoryInput {
  name: String!
  displayName: String!
  description: String
  sortOrder: Int = 0
}

input UpdateTaskCategoryInput {
  categoryId: ID!
  name: String
  displayName: String
  description: String
  isActive: Boolean
  sortOrder: Int
}

# Tier Benefit Management
type TierBenefitResponse {
  id: ID!
  tierLevel: Int!
  benefitType: String!
  benefitValue: Float!
  description: String
  isActive: Boolean!
  createdAt: Time!
  updatedAt: Time!
}

input CreateTierBenefitInput {
  tierLevel: Int!
  benefitType: String!
  benefitValue: Float!
  description: String
  isActive: Boolean = true
}

input UpdateTierBenefitInput {
  tierBenefitId: ID!
  tierLevel: Int
  benefitType: String
  benefitValue: Float
  description: String
  isActive: Boolean
}
