package middlewares

import (
	"context"
	"strings"

	"github.com/gin-gonic/gin"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
)

// Define a key for storing gin.Context in context.Context
type contextKey string

const GinContextKey contextKey = "ginContext"

// AdminApiKeyAuth middleware for validating API key authentication for admin GraphQL
func AdminApiKeyAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		apiKey := ctx.GetHeader("x-api-key")
		if apiKey == "" {
			// Try alternative header names
			apiKey = ctx.GetHeader("X-API-Key")
			if apiKey == "" {
				apiKey = ctx.GetHeader("Authorization")
				if strings.HasPrefix(apiKey, "Bearer ") {
					apiKey = strings.TrimPrefix(apiKey, "Bearer ")
				}
			}
		}

		// Validate API key
		if apiKey == "" || apiKey != global.GVA_CONFIG.Admin.InternalAPIKey {
			ctx.JSON(401, gin.H{
				"error": "unauthorized",
				"message": "invalid or missing API key for admin access",
			})
			ctx.Abort()
			return
		}

		// Set admin context for GraphQL resolvers
		wrappedCtx := context.WithValue(ctx.Request.Context(), "isAdmin", true)
		wrappedCtx = context.WithValue(wrappedCtx, "adminApiKey", apiKey)
		ctx.Request = ctx.Request.WithContext(wrappedCtx)
		ctx.Next()
	}
}

// GinGqlContext middleware to add gin.Context to context.Context of graphql
func GinGqlContext() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := context.WithValue(c.Request.Context(), GinContextKey, c)
		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}
