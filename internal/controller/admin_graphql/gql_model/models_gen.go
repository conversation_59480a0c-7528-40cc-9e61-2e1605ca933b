// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package gql_model

import (
	"bytes"
	"fmt"
	"io"
	"strconv"
	"time"
)

type ActivityTask struct {
	ID             string            `json:"id"`
	Name           string            `json:"name"`
	Description    *string           `json:"description,omitempty"`
	Category       *TaskCategory     `json:"category"`
	Type           TaskType          `json:"type"`
	Frequency      TaskFrequency     `json:"frequency"`
	PointsReward   int               `json:"pointsReward"`
	CashbackReward float64           `json:"cashbackReward"`
	MaxCompletions *int              `json:"maxCompletions,omitempty"`
	IsActive       bool              `json:"isActive"`
	StartDate      *time.Time        `json:"startDate,omitempty"`
	EndDate        *time.Time        `json:"endDate,omitempty"`
	Requirements   *TaskRequirements `json:"requirements,omitempty"`
	CreatedAt      time.Time         `json:"createdAt"`
	UpdatedAt      time.Time         `json:"updatedAt"`
}

type AdminStatsInput struct {
	StartDate time.Time `json:"startDate"`
	EndDate   time.Time `json:"endDate"`
}

type AdminTaskCompletionStats struct {
	TaskCompletions []*TaskCompletionStat `json:"taskCompletions"`
	StartDate       time.Time             `json:"startDate"`
	EndDate         time.Time             `json:"endDate"`
	TotalTasks      int                   `json:"totalTasks"`
}

type AdminTaskCompletionStatsResponse struct {
	Success bool                      `json:"success"`
	Message string                    `json:"message"`
	Data    *AdminTaskCompletionStats `json:"data,omitempty"`
}

type AdminTierDistributionResponse struct {
	Success bool                    `json:"success"`
	Message string                  `json:"message"`
	Data    []*TierDistributionStat `json:"data"`
}

type AdminUserActivityStats struct {
	DailyCompletions []*DailyCompletionStat `json:"dailyCompletions"`
	StartDate        time.Time              `json:"startDate"`
	EndDate          time.Time              `json:"endDate"`
}

type AdminUserActivityStatsResponse struct {
	Success bool                    `json:"success"`
	Message string                  `json:"message"`
	Data    *AdminUserActivityStats `json:"data,omitempty"`
}

type CreateTaskCategoryInput struct {
	Name        string  `json:"name"`
	DisplayName string  `json:"displayName"`
	Description *string `json:"description,omitempty"`
	SortOrder   *int    `json:"sortOrder,omitempty"`
}

type CreateTaskInput struct {
	Name           string                 `json:"name"`
	Description    *string                `json:"description,omitempty"`
	CategoryID     string                 `json:"categoryId"`
	Type           TaskType               `json:"type"`
	Frequency      TaskFrequency          `json:"frequency"`
	PointsReward   int                    `json:"pointsReward"`
	CashbackReward float64                `json:"cashbackReward"`
	MaxCompletions *int                   `json:"maxCompletions,omitempty"`
	IsActive       *bool                  `json:"isActive,omitempty"`
	StartDate      *time.Time             `json:"startDate,omitempty"`
	EndDate        *time.Time             `json:"endDate,omitempty"`
	Requirements   *TaskRequirementsInput `json:"requirements,omitempty"`
}

type CreateTierBenefitInput struct {
	TierLevel    int     `json:"tierLevel"`
	BenefitType  string  `json:"benefitType"`
	BenefitValue float64 `json:"benefitValue"`
	Description  *string `json:"description,omitempty"`
	IsActive     *bool   `json:"isActive,omitempty"`
}

type DailyCompletionStat struct {
	Date            string `json:"date"`
	CompletionCount int    `json:"completionCount"`
}

type Mutation struct {
}

type Query struct {
}

type TaskCategory struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	DisplayName string    `json:"displayName"`
	Description *string   `json:"description,omitempty"`
	IsActive    bool      `json:"isActive"`
	SortOrder   int       `json:"sortOrder"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

type TaskCompletionStat struct {
	TaskName        string `json:"taskName"`
	CompletionCount int    `json:"completionCount"`
}

type TaskRequirements struct {
	MinTradeVolume *float64 `json:"minTradeVolume,omitempty"`
	MinTradeCount  *int     `json:"minTradeCount,omitempty"`
	SocialAction   *string  `json:"socialAction,omitempty"`
	ReferralCount  *int     `json:"referralCount,omitempty"`
	LoginDays      *int     `json:"loginDays,omitempty"`
	CustomData     *string  `json:"customData,omitempty"`
}

type TaskRequirementsInput struct {
	MinTradeVolume *float64 `json:"minTradeVolume,omitempty"`
	MinTradeCount  *int     `json:"minTradeCount,omitempty"`
	SocialAction   *string  `json:"socialAction,omitempty"`
	ReferralCount  *int     `json:"referralCount,omitempty"`
	LoginDays      *int     `json:"loginDays,omitempty"`
	CustomData     *string  `json:"customData,omitempty"`
}

type TierBenefitResponse struct {
	ID           string    `json:"id"`
	TierLevel    int       `json:"tierLevel"`
	BenefitType  string    `json:"benefitType"`
	BenefitValue float64   `json:"benefitValue"`
	Description  *string   `json:"description,omitempty"`
	IsActive     bool      `json:"isActive"`
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

type TierDistributionStat struct {
	TierLevel int `json:"tierLevel"`
	UserCount int `json:"userCount"`
}

type UpdateTaskCategoryInput struct {
	CategoryID  string  `json:"categoryId"`
	Name        *string `json:"name,omitempty"`
	DisplayName *string `json:"displayName,omitempty"`
	Description *string `json:"description,omitempty"`
	IsActive    *bool   `json:"isActive,omitempty"`
	SortOrder   *int    `json:"sortOrder,omitempty"`
}

type UpdateTaskInput struct {
	TaskID         string                 `json:"taskId"`
	Name           *string                `json:"name,omitempty"`
	Description    *string                `json:"description,omitempty"`
	CategoryID     *string                `json:"categoryId,omitempty"`
	Type           *TaskType              `json:"type,omitempty"`
	Frequency      *TaskFrequency         `json:"frequency,omitempty"`
	PointsReward   *int                   `json:"pointsReward,omitempty"`
	CashbackReward *float64               `json:"cashbackReward,omitempty"`
	MaxCompletions *int                   `json:"maxCompletions,omitempty"`
	IsActive       *bool                  `json:"isActive,omitempty"`
	StartDate      *time.Time             `json:"startDate,omitempty"`
	EndDate        *time.Time             `json:"endDate,omitempty"`
	Requirements   *TaskRequirementsInput `json:"requirements,omitempty"`
}

type UpdateTierBenefitInput struct {
	TierBenefitID string   `json:"tierBenefitId"`
	TierLevel     *int     `json:"tierLevel,omitempty"`
	BenefitType   *string  `json:"benefitType,omitempty"`
	BenefitValue  *float64 `json:"benefitValue,omitempty"`
	Description   *string  `json:"description,omitempty"`
	IsActive      *bool    `json:"isActive,omitempty"`
}

type UserTierInfo struct {
	UserID         string     `json:"userId"`
	CurrentTier    int        `json:"currentTier"`
	TotalPoints    int        `json:"totalPoints"`
	TotalCashback  float64    `json:"totalCashback"`
	CompletedTasks int        `json:"completedTasks"`
	LastActivityAt *time.Time `json:"lastActivityAt,omitempty"`
}

type TaskFrequency string

const (
	TaskFrequencyDaily   TaskFrequency = "DAILY"
	TaskFrequencyWeekly  TaskFrequency = "WEEKLY"
	TaskFrequencyMonthly TaskFrequency = "MONTHLY"
	TaskFrequencyOneTime TaskFrequency = "ONE_TIME"
)

var AllTaskFrequency = []TaskFrequency{
	TaskFrequencyDaily,
	TaskFrequencyWeekly,
	TaskFrequencyMonthly,
	TaskFrequencyOneTime,
}

func (e TaskFrequency) IsValid() bool {
	switch e {
	case TaskFrequencyDaily, TaskFrequencyWeekly, TaskFrequencyMonthly, TaskFrequencyOneTime:
		return true
	}
	return false
}

func (e TaskFrequency) String() string {
	return string(e)
}

func (e *TaskFrequency) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskFrequency(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskFrequency", str)
	}
	return nil
}

func (e TaskFrequency) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TaskFrequency) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TaskFrequency) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TaskType string

const (
	TaskTypeTrading     TaskType = "TRADING"
	TaskTypeSocial      TaskType = "SOCIAL"
	TaskTypeReferral    TaskType = "REFERRAL"
	TaskTypeLogin       TaskType = "LOGIN"
	TaskTypeMarketCheck TaskType = "MARKET_CHECK"
	TaskTypeCustom      TaskType = "CUSTOM"
)

var AllTaskType = []TaskType{
	TaskTypeTrading,
	TaskTypeSocial,
	TaskTypeReferral,
	TaskTypeLogin,
	TaskTypeMarketCheck,
	TaskTypeCustom,
}

func (e TaskType) IsValid() bool {
	switch e {
	case TaskTypeTrading, TaskTypeSocial, TaskTypeReferral, TaskTypeLogin, TaskTypeMarketCheck, TaskTypeCustom:
		return true
	}
	return false
}

func (e TaskType) String() string {
	return string(e)
}

func (e *TaskType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskType", str)
	}
	return nil
}

func (e TaskType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TaskType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TaskType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}
