package admin_graphql

import (
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/resolvers"
)

// This file will not be regenerated automatically.
//
// It serves as dependency injection for your app, add any dependencies you require here.

type Resolver struct {
	ActivityCashbackService *resolvers.ActivityCashbackResolver
}

func NewRootResolver() *Resolver {
	return &Resolver{
		ActivityCashbackService: resolvers.NewActivityCashbackResolver(),
	}
}
