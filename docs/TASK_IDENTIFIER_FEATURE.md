# Task Identifier Feature

## Overview

The Task Identifier feature has been added to the Activity Cashback System to enable proper task processing and identification. This feature allows admin users to specify unique identifiers when creating tasks, which are essential for the task processing system.

## Problem Solved

Previously, when creating tasks through the Admin API, the `taskIdentifier` field was not being set, causing errors during task processing. The task processing system requires this identifier to:

1. Validate task eligibility
2. Route tasks to appropriate handlers
3. Apply business logic specific to each task type
4. Ensure consistent task behavior across the system

## Changes Made

### 1. GraphQL Schema Updates

**File**: `internal/controller/graphql/schemas/activity_cashback.gql`

Added `taskIdentifier` field to `CreateTaskInput`:

```graphql
input CreateTaskInput {
  categoryId: ID!
  name: String!
  description: String
  taskType: TaskType!
  frequency: TaskFrequency!
  taskIdentifier: String # Optional unique identifier for task processing
  points: Int!
  # ... other fields
}
```

### 2. GraphQL Model Updates

**File**: `internal/controller/graphql/gql_model/models_gen.go`

Added `TaskIdentifier` field to `CreateTaskInput` struct:

```go
type CreateTaskInput struct {
    CategoryID         string        `json:"categoryId"`
    Name               string        `json:"name"`
    // ... other fields
    TaskIdentifier     *string       `json:"taskIdentifier,omitempty"`
    Points             int           `json:"points"`
    // ... other fields
}
```

### 3. Admin Resolver Updates

**File**: `internal/controller/graphql/resolvers/admin_activity_cashback.go`

Added validation and processing for `TaskIdentifier`:

```go
// Set TaskIdentifier if provided
if input.TaskIdentifier != nil && *input.TaskIdentifier != "" {
    identifier := model.TaskIdentifier(*input.TaskIdentifier)
    // Validate that the identifier is valid
    if model.IsValidTaskIdentifier(identifier) {
        task.TaskIdentifier = &identifier
    } else {
        return nil, fmt.Errorf("invalid task identifier: %s", *input.TaskIdentifier)
    }
}
```

### 4. Response Conversion Updates

**File**: `internal/controller/graphql/resolvers/activity_cashback.go`

Updated `convertActivityTaskToGQL` function to include `TaskIdentifier`:

```go
if task.TaskIdentifier != nil {
    taskIdentifier := string(*task.TaskIdentifier)
    gqlTask.TaskIdentifier = &taskIdentifier
}
```

### 5. Database Schema Updates

**Migration**: `migrations/20250828093110.sql`

Added `task_identifier` column to `activity_tasks` table:

```sql
ALTER TABLE "public"."activity_tasks" ADD COLUMN "task_identifier" character varying(50) NULL;
CREATE INDEX "idx_activity_tasks_task_identifier" ON "public"."activity_tasks" ("task_identifier");
```

### 6. Test Database Updates

**File**: `internal/test/config.go`

Updated test database schema to include `task_identifier` column:

```sql
CREATE TABLE IF NOT EXISTS activity_tasks (
    -- ... other columns
    task_identifier TEXT,
    -- ... other columns
)
```

## Usage

### Creating a Task with Task Identifier

```graphql
mutation {
  createTask(input: {
    categoryId: "1"
    name: "Daily Check-in Task"
    description: "Check in daily to earn points"
    taskType: DAILY
    frequency: DAILY
    taskIdentifier: "DAILY_CHECKIN"  # Valid identifier from registry
    points: 5
    sortOrder: 1
  }) {
    id
    name
    taskIdentifier
    points
    isActive
  }
}
```

### Valid Task Identifiers

The system validates task identifiers against a predefined registry. Valid identifiers include:

**Daily Tasks:**
- `DAILY_CHECKIN`
- `MEME_TRADE_DAILY`
- `PERPETUAL_TRADE_DAILY`
- `MARKET_PAGE_VIEW`
- `CONSECUTIVE_CHECKIN`
- `CONSECUTIVE_TRADING_DAYS`

**Community Tasks:**
- `TWITTER_FOLLOW`
- `TWITTER_RETWEET`
- `TWITTER_LIKE`
- `TELEGRAM_JOIN`
- `INVITE_FRIENDS`
- `SHARE_REFERRAL`

**Trading Tasks:**
- `TRADING_POINTS`
- `ACCUMULATED_TRADING_10K`
- `ACCUMULATED_TRADING_50K`
- `ACCUMULATED_TRADING_100K`
- `ACCUMULATED_TRADING_500K`

### Error Handling

If an invalid task identifier is provided, the API will return an error:

```json
{
  "errors": [
    {
      "message": "invalid task identifier: INVALID_TASK_ID"
    }
  ]
}
```

## Testing

The feature includes comprehensive tests:

1. **TestCreateTaskResolver**: Tests successful task creation with valid identifier
2. **TestCreateTaskWithInvalidIdentifier**: Tests error handling for invalid identifiers

Run tests with:

```bash
go test ./internal/controller/graphql/resolvers -v -run TestCreateTask
```

## Benefits

1. **Proper Task Processing**: Tasks can now be processed correctly by the task registry system
2. **Validation**: Invalid task identifiers are caught at creation time
3. **Consistency**: Ensures tasks use predefined identifiers from the registry
4. **Backward Compatibility**: The field is optional, so existing functionality continues to work
5. **Type Safety**: Uses strongly-typed identifiers with validation

## Migration Notes

- The database migration adds the `task_identifier` column as nullable
- Existing tasks without identifiers will continue to work
- New tasks can optionally specify identifiers for enhanced functionality
- The feature is backward compatible with existing API clients
