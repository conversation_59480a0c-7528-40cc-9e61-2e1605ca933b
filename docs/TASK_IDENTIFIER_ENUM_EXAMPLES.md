# Task Identifier Enum Examples

## Overview

The Task Identifier feature has been upgraded to use GraphQL enums instead of strings, providing better type safety and easier selection in tools like Postman.

## GraphQL Schema

### TaskIdentifier Enum

```graphql
enum TaskIdentifier {
  # Daily Tasks
  DAILY_CHECKIN
  MEME_TRADE_DAILY
  PERPETUAL_TRADE_DAILY
  MARKET_PAGE_VIEW
  CONSECUTIVE_CHECKIN
  CONSECUTIVE_TRADING_DAYS
  
  # Community Tasks
  TWITTER_FOLLOW
  TWITTER_RETWEET
  TWITTER_LIKE
  TELEGRAM_JOIN
  INVITE_FRIENDS
  SHARE_REFERRAL
  
  # Trading Tasks
  TRADING_POINTS
  ACCUMULATED_TRADING_10K
  ACCUMULATED_TRADING_50K
  ACCUMULATED_TRADING_100K
  ACCUMULATED_TRADING_500K
}
```

### Updated Input Types

```graphql
input CreateTaskInput {
  categoryId: ID!
  name: String!
  description: String
  taskType: TaskType!
  frequency: TaskFrequency!
  taskIdentifier: TaskIdentifier # Now uses enum instead of String
  points: Int!
  maxCompletions: Int
  resetPeriod: String
  conditions: String
  actionTarget: String
  verificationMethod: String
  externalLink: String
  startDate: Time
  endDate: Time
  sortOrder: Int
}
```

### Updated Response Types

```graphql
type ActivityTask {
  id: ID!
  categoryId: ID!
  name: String!
  description: String
  taskType: TaskType!
  frequency: TaskFrequency!
  taskIdentifier: TaskIdentifier # Now uses enum instead of String
  points: Int!
  # ... other fields
}
```

## Usage Examples

### 1. Create Daily Check-in Task

```graphql
mutation {
  createTask(input: {
    categoryId: "1"
    name: "Daily Check-in"
    description: "Check in daily to earn points"
    taskType: DAILY
    frequency: DAILY
    taskIdentifier: DAILY_CHECKIN
    points: 5
    sortOrder: 1
  }) {
    id
    name
    taskIdentifier
    points
    isActive
  }
}
```

### 2. Create Trading Task

```graphql
mutation {
  createTask(input: {
    categoryId: "3"
    name: "Complete Meme Trade"
    description: "Complete at least one meme trade daily"
    taskType: DAILY
    frequency: DAILY
    taskIdentifier: MEME_TRADE_DAILY
    points: 200
    sortOrder: 2
  }) {
    id
    name
    taskIdentifier
    points
    isActive
  }
}
```

### 3. Create Community Task

```graphql
mutation {
  createTask(input: {
    categoryId: "2"
    name: "Follow on Twitter"
    description: "Follow our official Twitter account"
    taskType: ONE_TIME
    frequency: ONE_TIME
    taskIdentifier: TWITTER_FOLLOW
    points: 50
    externalLink: "https://twitter.com/xbit_dex"
  }) {
    id
    name
    taskIdentifier
    points
    externalLink
    isActive
  }
}
```

### 4. Create Task Without Identifier (Optional)

```graphql
mutation {
  createTask(input: {
    categoryId: "1"
    name: "Custom Task"
    description: "A custom task without predefined identifier"
    taskType: DAILY
    frequency: DAILY
    points: 10
  }) {
    id
    name
    taskIdentifier # Will be null
    points
    isActive
  }
}
```

### 5. Query Tasks with Identifiers

```graphql
query {
  tasksByCategory(categoryName: "daily") {
    id
    name
    taskIdentifier
    points
    isActive
  }
}
```

## Benefits of Enum Approach

### 1. Type Safety
- GraphQL validates enum values at schema level
- Invalid identifiers are caught before reaching the resolver
- IDE/Postman provides autocomplete for valid values

### 2. Better Developer Experience
- Dropdown selection in GraphQL Playground/Postman
- Clear documentation of available identifiers
- Prevents typos and invalid values

### 3. API Consistency
- Standardized identifier values across the system
- Clear mapping between GraphQL enum and Go constants
- Easier maintenance and updates

## Postman Usage

When using Postman with GraphQL:

1. **Query Variables**: Use enum values directly
```json
{
  "input": {
    "categoryId": "1",
    "name": "Daily Check-in",
    "taskType": "DAILY",
    "frequency": "DAILY",
    "taskIdentifier": "DAILY_CHECKIN",
    "points": 5
  }
}
```

2. **Autocomplete**: Postman will show available enum values when typing
3. **Validation**: Invalid enum values will be rejected at GraphQL level

## Migration Notes

### From String to Enum

**Before (String)**:
```graphql
taskIdentifier: "DAILY_CHECKIN"  # String value
```

**After (Enum)**:
```graphql
taskIdentifier: DAILY_CHECKIN    # Enum value (no quotes)
```

### Backward Compatibility

- Existing tasks with string identifiers continue to work
- New tasks should use enum values
- GraphQL introspection shows available enum values
- API clients can discover valid identifiers dynamically

## Error Handling

### Invalid Enum Value
GraphQL will return a validation error before reaching the resolver:

```json
{
  "errors": [
    {
      "message": "Value \"INVALID_TASK\" does not exist in \"TaskIdentifier\" enum.",
      "locations": [{"line": 8, "column": 5}]
    }
  ]
}
```

### Missing Identifier
Tasks can be created without identifiers (optional field):

```graphql
mutation {
  createTask(input: {
    categoryId: "1"
    name: "Custom Task"
    taskType: DAILY
    frequency: DAILY
    points: 10
    # taskIdentifier omitted - will be null
  }) {
    id
    name
    taskIdentifier # null
  }
}
```

This enum approach provides a much better developer experience while maintaining full backward compatibility.
